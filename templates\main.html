<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" />
    <title>AutoSpace</title>
</head>
<body>
    <!-- Your HTML (added data-labels for tooltips + ARIA for a11y) -->
    <div class="side-bar" role="tablist" aria-orientation="vertical">
    <span tabindex="0" class="tool-category material-symbols-outlined comparison" id="1" role="tab" aria-selected="true" data-label="Compare">text_compare</span>
    <span tabindex="0" class="tool-category material-symbols-outlined search" id="2" role="tab" aria-selected="false" data-label="Search">document_search</span>
    <span tabindex="0" class="tool-category material-symbols-outlined extraction" id="3" role="tab" aria-selected="false" data-label="Extract">export_notes</span>
    </div>
    
    <div class="main-content">
        <div class="tools-cards-section section">
                <h2></h2>
        </div>

        <div class = "middle-section section">
            <div class = "autospace-section section">
                <h2>AutoSpace</h2>
                <div class = "default-view">
                    please select a tool to view its information
                </div>
                
                <div class = "selection-view">
                    <span class = "tool-name"></span>
                    
                    <span class = "tool-description"></span>

                    <span class = "tool-buttons">

                        <button class = 'run-button'>
                            Run
                        </button>
                        
                        <label for="fileInput" class="file-label">📂 Upload File</label>
                        <input type="file" id="fileInput" accept=".txt">
                        
                        <button class = 'download-sample-button'>
                            Download Sample
                        </button>

                        <input type="checkbox" id="mode" name="mode" value="fast">
                        <label for="mode">Fast Mode</label><br>


                    </span>
                </div>
                
                <div class="sessions-section section">
                    <div class= 'sessions-container running-sessions'></div>
                    <div class= 'sessions-container finished-sessions'></div>
                </div>
            </div>
        </div>

    </div>
    
    <script>

        // directly after opening the page we need to update it with running and done sessions
        async function getAllSessions(){
                // first we need to clear the containers to avoid duplications                
                runningCardsContainer.innerHTML = '<h2>Active Sessions</h2>'
                finishedCardsContainer.innerHTML = '<h2>Finished Sessions</h2>'
                
                // sending request to get all sessions                                
                const response = await fetch(`/sessions-all/${currentUserId}`)
                const data = await response.json();
                const finished_sessions = data.Done
                const running_sessions = data.Running
                
                // creating cards for running sessions                 
                Object.keys(running_sessions).forEach(session_id => {
                    
                    const runCardReload = createSessionCard(session_id, currentUserId, running_sessions[session_id].progress, running_sessions[session_id].timestamp)                    
                    runningCardsContainer.appendChild(runCardReload)
                });
                 
                finished_sessions.forEach((session_data)=> {                
                const doneCardReload = createDoneCard(session_data[0], session_data[1])                
                finishedCardsContainer.appendChild(doneCardReload)
                })
            } 

        
        async function getToolsData(){
            const response = await fetch('/tools-data')
            const data = await response.json();
            toolsData = data
            categoryList.forEach((category)=>{
            category.addEventListener('click', ()=>{
                toolCardsSection.innerHTML = `<h1>${category.classList[2].toLocaleUpperCase()}</h1>`
                Object.keys(toolsData).forEach(tool_id => {
                    if (toolsData[tool_id][0].Category_ID.toString() == category.id){
                        toolCard = createToolCard(toolsData[tool_id], tool_id)
                        toolCardsSection.appendChild(toolCard)
                    }
                });
            })
        })
        }
        // function to run the tool and assigning done with request is back        
        async function runTool(session_id){                
                if (inputFile == null){
                    alert("Please upload a file")
                    return
                }

                const formData = new FormData();
                const modeCheckbox = document.getElementById('mode');
                formData.append('mode', modeCheckbox.checked ? 'fast' : 'normal');
                formData.append('file', inputFile);
                formData.append('tool_id', currentToolID);
                // sending request to run the tool and get the progress back                
                const response = await fetch(`/session-run/${session_id}-${currentUserId}`
                ,{method: 'POST', body: formData})

                const data = await response.json();
                inputFile = null;
                fileInput.value = "";
                return true
            }

        function createToolCard(toolCardData, toolId){
            const toolCard = document.createElement('div')
            toolCard.className = `tool-card`
            toolCard.id = toolId
            toolCard.classList.add(toolCardData[0].Tool_Name)
            toolCard.innerHTML = `<h3>${toolCardData[0].Name}</h3>`
            toolCard.addEventListener('click', ()=>{
                toolDescrtiption.innerHTML = toolCardData[0].Description
                toolName.innerHTML = toolCardData[0].Name
                spaceDefaultView.style.display = "none"
                spaceSelectionView.style.display = "flex"

                currentToolID = toolId
            })
            
            return toolCard
        }

        function createSessionCard(currentSessionId, currentUserId, progress, currentTime){            
            const currentRunCard = document.createElement('div')
            currentRunCard.className = `session-${currentSessionId} session-card`
            currentRunCard.id = currentSessionId
            currentRunCard.innerHTML = `<div class="title">Session #${currentSessionId}</div>`
            
            const refresh_button = create_refresh_button(currentSessionId)
            const kill_button = create_kill_button(currentSessionId)
            const progressCounter = create_progress_counter(progress)            

            const timeCard = create_time_label(currentTime)

            currentRunCard.appendChild(timeCard)            
            currentRunCard.appendChild(refresh_button)
            currentRunCard.appendChild(kill_button)
            currentRunCard.appendChild(progressCounter)
            
            refresh_button.addEventListener('click', async () => {
                const response = await fetch(`/session-refresh/${currentSessionId}-${currentUserId}`)
                const data = await response.json()
                progressCounter.textContent = `${data.progress}%`
                if (data.status == "Done"){
                    move_card_to_done(currentRunCard, data.status)                    
                }
            })

            kill_button.addEventListener('click', async () => {
                const response = await fetch(`/session-kill/${currentSessionId}-${currentUserId}`)
                const data = await response.json()
                move_card_to_done(currentRunCard, "Killed")
            })
                        
            return currentRunCard
        }


        function createDoneCard(currentSessionId, currentTime){
            const currentDoneCard = document.createElement('div')
            currentDoneCard.className = `done-session-${currentSessionId} session-card`
            currentDoneCard.innerHTML = `<div class="title">Session #${currentSessionId}</div>`
            const time = create_time_label(currentTime)
            currentDoneCard.appendChild(time)
            const download_button = create_download_button(currentSessionId)
            currentDoneCard.appendChild(download_button)
            return currentDoneCard
        }

        function create_refresh_button(session_id){
            // creating refresh button that tracks the session progress
            const currentRefreshButton = document.createElement('button')
            currentRefreshButton.className = `refresh-button`
            currentRefreshButton.dataset.sessionId = `${session_id}`
            currentRefreshButton.innerHTML = "REFRESH"
            return currentRefreshButton
        }

        function create_kill_button(session_id){
            // creating kill button that kills the session
            const currentKillButton = document.createElement('button')
            currentKillButton.className = `kill-button`
            currentKillButton.dataset.sessionId = `${session_id}`
            currentKillButton.innerHTML = "KILL"
            return currentKillButton
        }

        function create_progress_counter(progress){            
            const progressCounter = document.createElement('span')
            progressCounter.className = `progress-counter`
            progressCounter.innerHTML = `${progress}%`            
            return progressCounter
        }


        function move_card_to_done(currentRunCard, status){
            
            if (status == "Killed"){
                if (currentRunCard.parentElement === runningCardsContainer){
                    runningCardsContainer.removeChild(currentRunCard)
                    return
                }
                
            }

            const refresh_button = currentRunCard.querySelector('.refresh-button')
            const kill_button = currentRunCard.querySelector('.kill-button')
            const progressCounter = currentRunCard.querySelector('.progress-counter')
            
            const time = create_time_label()
            currentRunCard.appendChild(time)            

            progressCounter.textContent = `${status}`            
            currentRunCard.classList.add('done-session')
            

            const download_button = create_download_button(currentRunCard.id)
            currentRunCard.appendChild(download_button)

            currentRunCard.removeChild(refresh_button)
            currentRunCard.removeChild(kill_button)

            finishedCardsContainer.appendChild(currentRunCard)            
        }

        function create_download_button(session_id){
            const download_button = document.createElement('button');
            download_button.className = `download-button`;
            download_button.innerHTML = "DOWNLOAD";

            download_button.addEventListener('click', async ()=>{
                const response = await fetch(`/session-download/${session_id}-${currentUserId}`);
                if (!response.ok) { alert("Download failed."); return; }
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `output_${session_id}_${currentUserId}.txt`; // nice filename
                document.body.appendChild(a);
                a.click();
                a.remove();
                URL.revokeObjectURL(url);
            });
            return download_button;
        }


        function create_time_label(currentTime){

            const time = document.createElement('span')

            time.className = `time`
            if (currentTime == null || currentTime == undefined){
                time.innerHTML = getCurrentTimeHHMM()                
            }
            else{
                time.innerHTML = currentTime                
            }
            return time
        }

        function getCurrentTimeHHMM() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, "0");   // ensure 2 digits
            const minutes = String(now.getMinutes()).padStart(2, "0"); // ensure 2 digits
            return `${hours}:${minutes}`;
        }

        function makeSessionId() {
            // Example: "S812345-1427" (random + HHMM)
            const rand = Math.floor(100000 + Math.random() * 900000);
            const now = getCurrentTimeHHMM();          // "14:27"
            const hhmm = now.replace(/:/g, "");        // "1427"
            return `S${rand}-${hhmm}`;
        }

        // declaring the elements we will use
        let currentUserId = "U00001" 
        const runButton = document.querySelector(".run-button")
        const runningCardsContainer= document.querySelector(".running-sessions")    
        const finishedCardsContainer = document.querySelector(".finished-sessions")
        // const userList = document.querySelectorAll(".user-selection li")
        const categoryList = document.querySelectorAll(".tool-category")
        const fileInput = document.getElementById('fileInput');
        let inputFile = null
        const toolCardsSection = document.querySelector(".tools-cards-section")
        let currentToolID = null
        let currentToolDescription = null
        const spaceDefaultView = document.querySelector(".default-view")
        const spaceSelectionView = document.querySelector(".selection-view")
        const toolDescrtiption = document.querySelector(".tool-description")
        const toolName = document.querySelector(".tool-name")
        
        spaceSelectionView.style.display = "none"
        getToolsData()
        // load all sessions [finished and running] to see realtime update
        getAllSessions()

        fileInput.addEventListener('change', (event)=> {
            inputFile = event.target.files[0]
        });
       

        // adding the functionality for the runButton
        runButton.addEventListener('click', async ()=>{
            // creating a random number for session id
            const currentTime = getCurrentTimeHHMM()
            session_id = makeSessionId()
            // before creating the card we send request to run tools and make sure we get the response to create the card
            const session_status  = await runTool(session_id)

            if (session_status){
            // creating the session card and assigning the session id
            const runCard = createSessionCard(session_id, currentUserId, 0, currentTime)            
            
            runningCardsContainer.appendChild(runCard)}

        })
    </script>
</body>
</html>